from django.contrib import admin
from .models import ManufacturingEvent, EventRequestLog, WorkOrder


@admin.register(ManufacturingEvent)
class ManufacturingEventAdmin(admin.ModelAdmin):
    list_display = ('serial_number', 'form', 'product', 'board', 'work_order', 'inspection_status', 'created_at')
    search_fields = ('board', 'work_order', 'serial_number')
    list_filter = ('product', 'created_at', 'form')

    fieldsets = (
        ('Basic Information', {
            'fields': ('serial_number', 'product', 'board', 'work_order', 'form')
        }),
        ('Status', {
            'fields': ('inspection_status', 'validation_status', 'validation_errors')
        }),
        ('Event Details', {
            'fields': ('event_type', 'next_action', 'event_data', 'parsed_serial_data')
        }),
        ('Timestamps', {
            'fields': ('timestamp',)
        }),
        ('References', {
            'fields': ('created_by', 'scanner')
        })
    )


@admin.register(EventRequestLog)
class EventRequestLogAdmin(admin.ModelAdmin):
    list_display = ('id', 'url', 'method', 'response_code', 'created_at',)
    search_fields = ('url', 'method')
    list_filter = ('created_at', 'response_code')

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return False


@admin.register(WorkOrder)
class WorkOrderAdmin(admin.ModelAdmin):
    list_display = ('order_no', 'line', 'part_no', 'product', 'customer', 'order_date', 'plan', 'actual', 'cf')
    search_fields = ('order_no', 'part_no', 'customer')
    list_filter = ('line', 'order_date', 'created_at')
    date_hierarchy = 'order_date'
    ordering = ('-order_date', 'line')

    fieldsets = (
        ('Work Order Information', {
            'fields': ('order_no', 'line', 'part_no', 'product', 'customer', 'order_date')
        }),
        ('Production Details', {
            'fields': ('plan', 'actual', 'cf')
        }),
        ('Metadata', {
            'fields': ('created_by', 'created_at', 'updated_at')
        })
    )

    readonly_fields = ('created_at', 'updated_at')

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('created_by')

    def has_delete_permission(self, request, obj=None):
        # Only superusers can delete work orders
        return request.user.is_superuser
