"""Strategies for serial number parsing.

This module defines the different strategies that can be used to parse serial numbers.
"""

import re
import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Callable

from operation.services.serial_parsing.models import ParsingContext, ParsedSerial

logger = logging.getLogger('serial_parsing')


class ParsingStrategy(ABC):
    """Abstract base class for serial number parsing strategies."""
    
    @property
    @abstractmethod
    def strategy_name(self) -> str:
        """Get the name of the strategy."""
        pass
    
    @abstractmethod
    def parse(self, context: ParsingContext) -> ParsedSerial:
        """Parse a serial number using this strategy.
        
        Args:
            context: The parsing context containing the serial number and additional information
            
        Returns:
            ParsedSerial object with parsed components and any validation errors
        """
        pass


class SimpleRegexStrategy(ParsingStrategy):
    """Strategy for parsing serial numbers using regular expressions."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize the strategy with a configuration.
        
        Args:
            config: A dictionary containing the regex pattern and field mapping
                Example:
                {
                    "pattern": "^(?P<part_code>[^#]+)#(?P<work_order>\\d{7})(?P<board>\\d+)$",
                    "field_mapping": {
                        "part_code": {
                            "required": true,
                            "transform": "trim"
                        },
                        "work_order": {
                            "required": true,
                            "transform": "string"
                        },
                        "board": {
                            "required": false,
                            "default_value": 1,
                            "transform": "integer"
                        }
                    }
                }
        """
        self.config = config
        self.pattern = config.get('pattern')
        self.field_mapping = config.get('field_mapping', {})
        
        # Compile the regex pattern
        try:
            self.regex = re.compile(self.pattern) if self.pattern else None
            print("self.regex", self.regex)
        except re.error as e:
            logger.error(f"Invalid regex pattern: {e}")
            self.regex = None
    
    @property
    def strategy_name(self) -> str:
        """Get the name of the strategy."""
        return 'regex_based'
    
    def parse(self, context: ParsingContext) -> ParsedSerial:
        """Parse a serial number using regex pattern.
        
        Args:
            context: The parsing context containing the serial number
            
        Returns:
            ParsedSerial object with parsed components and any validation errors
        """
        result = ParsedSerial(
            serial_number=context.serial_number,
            strategy_name=self.strategy_name
        )

        print("__parsed result__", result)

        if not self.regex:
            result.validation_errors.append("Invalid regex pattern configuration")
            return result
        
        # Try to match the pattern
        match = self.regex.match(context.serial_number)
        if not match:
            result.validation_errors.append(f"Serial number does not match pattern: {self.pattern}")
            return result
        
        # Extract named groups from the match
        extracted_fields = match.groupdict()
        print("extracted_fields", extracted_fields)
        
        # Process each field according to the field mapping
        for field_name, field_config in self.field_mapping.items():
            print("field_name is", field_name)
            # Check if the field is in the extracted fields
            if field_name in extracted_fields:
                value = extracted_fields[field_name]
                
                # Apply transformations
                transform_type = field_config.get('transform')
                if transform_type:
                    value = self._apply_transform(value, transform_type)
                
                # Add to result
                result.components[field_name] = value
            else:
                print("field_name ! in", field_name)
                # Check if the field is required
                if field_config.get('required', False):
                    result.validation_errors.append(f"Required field '{field_name}' not found in serial number")
                # Use default value if provided
                elif 'default_value' in field_config:
                    result.components[field_name] = field_config['default_value']
        
        return result
    
    def _apply_transform(self, value: str, transform_type: str) -> Any:
        """Apply a transformation to a value.
        
        Args:
            value: The value to transform
            transform_type: The type of transformation to apply
            
        Returns:
            The transformed value
        """
        if transform_type == 'trim':
            return value.strip()
        elif transform_type == 'integer':
            try:
                return int(value)
            except ValueError:
                return value
        elif transform_type == 'string':
            return str(value)
        else:
            logger.warning(f"Unknown transform type: {transform_type}")
            return value


class CustomLogicStrategy(ParsingStrategy):
    """Strategy for parsing serial numbers using custom logic.
    
    This strategy implements the legacy parsing logic for backward compatibility.
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """Initialize the strategy with an optional configuration.
        
        Args:
            config: Optional configuration for the custom logic
        """
        self.config = config or {}
        self.parser_name = self.config.get('parser_name', 'legacy_parser')
    
    @property
    def strategy_name(self) -> str:
        """Get the name of the strategy."""
        return 'custom_logic'
    
    def parse(self, context: ParsingContext) -> ParsedSerial:
        """Parse a serial number using custom logic.
        
        Args:
            context: The parsing context containing the serial number
            
        Returns:
            ParsedSerial object with parsed components and any validation errors
        """
        result = ParsedSerial(
            serial_number=context.serial_number,
            strategy_name=self.strategy_name
        )
        print("__parsed result in custom logic strategy__", result)
        
        # Legacy parsing logic (similar to the existing _legacy_parse_serial_number)
        serial_number = context.serial_number
        
        if not serial_number:
            result.validation_errors.append('Serial number is required')
            return result
        
        # Check if serial number contains '#' separator
        if '#' not in serial_number:
            result.validation_errors.append('Invalid serial number format: missing # separator')
            return result
        
        # Split by '#'
        parts = serial_number.split('#')
        if len(parts) != 2:
            result.validation_errors.append('Invalid serial number format: should have exactly one # separator')
            return result
        
        part_code, sequence = parts
        result.components['part_code'] = part_code
        
        # Extract work order and board number
        if len(sequence) >= 11:  # At least 7 digits for work order + 4 digits for board
            result.components['work_order'] = sequence[-11:-4]  # 7 digits before last 4
            result.components['board'] = sequence[-4:]  # Last 4 digits
            
            # Validate work order (should be 7 digits)
            if not re.match(r'^\d{7}$', result.components['work_order']):
                result.validation_errors.append('Work order should be 7 digits')
            
            # Validate board number (should be between 1 and 9999)
            try:
                board_num = int(result.components['board'])
                result.components['board'] = board_num  # Convert to integer
                if not (1 <= board_num <= 9999):
                    result.validation_errors.append('Board number should be between 1 and 9999')
            except ValueError:
                result.validation_errors.append('Board number should be numeric')
        else:
            result.validation_errors.append('Invalid sequence format: too short')

        print("__parsed result after validations__", result)
        return result


# Factory function to create a strategy based on configuration
def create_strategy(config: Dict[str, Any]) -> ParsingStrategy:
    """Create a parsing strategy based on configuration.
    
    Args:
        config: A dictionary containing the strategy configuration
        
    Returns:
        A ParsingStrategy instance
    """
    strategy_type = config.get('strategy')
    
    if strategy_type == 'regex_based':
        return SimpleRegexStrategy(config)
    elif strategy_type == 'custom_logic':
        return CustomLogicStrategy(config)
    else:
        logger.warning(f"Unknown strategy type: {strategy_type}, falling back to custom logic")
        return CustomLogicStrategy()