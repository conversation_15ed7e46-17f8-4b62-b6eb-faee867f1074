# Generated by Django 5.1 on 2025-06-29 09:51

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('operation', '0012_workorder_product_workorder_remaining_quantity'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='eventrequestlog',
            options={'ordering': ['-id']},
        ),
        migrations.AlterModelOptions(
            name='fifoviolationlog',
            options={'ordering': ['-id']},
        ),
        migrations.AlterModelOptions(
            name='workorder',
            options={'ordering': ['-updated_at']},
        ),
        migrations.AddField(
            model_name='manufacturingevent',
            name='parsed_serial_data',
            field=models.JSONField(blank=True, help_text='Parsed serial number data including components like part_code, work_order, board, etc.', null=True),
        ),
    ]
