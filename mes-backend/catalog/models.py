from django.db import models
from core.models import BaseEntity, BaseModel
from django.core.exceptions import ValidationError


class Commodity(BaseModel):
    """
    Commodity data model
    """
    name = models.CharField(max_length=255)
    description = models.TextField(null=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        db_table = 'mes_commodities'
        ordering = ['-updated_at']

    def __str__(self):
        return self.name


class Component(BaseModel):
    """
    Model to store component information
    """
    code = models.CharField(max_length=100, unique=True)
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    specification = models.JSONField(default=dict, blank=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        db_table = 'mes_components'
        ordering = ['-updated_at']

    def __str__(self):
        return f"{self.code} - {self.name}"


class ProductType(models.Model):
    """Model for product types"""
    code = models.CharField(max_length=50, unique=True)
    label = models.CharField(max_length=100)
    serial_decipher_logic = models.JSONField(null=True, blank=True, help_text="Configuration for serial number parsing")
    fifo_enabled = models.BooleanField(default=False, help_text="Enable FIFO tracking for this product type")
    fifo_strict_enforcement = models.BooleanField(default=True, help_text="Strictly enforce FIFO rules for this product type")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'mes_product_type'
        ordering = ['-updated_at']

    def __str__(self):
        return self.label

    def clean(self):
        print("__cleaning product type__")
        """Validate the serial_decipher_logic JSON structure"""
        if self.serial_decipher_logic:
            try:
                logic = self.serial_decipher_logic
                
                # Check if strategy_config is provided
                if 'strategy_config' in logic:
                    strategy_config = logic['strategy_config']
                    # Validate strategy_config
                    if not isinstance(strategy_config, dict):
                        raise ValidationError({'serial_decipher_logic': 'strategy_config must be a dictionary'})
                    
                    # Check strategy type
                    strategy_type = strategy_config.get('strategy')
                    if not strategy_type:
                        raise ValidationError({'serial_decipher_logic': 'strategy_config must include strategy type'})
                    
                    # Validate regex_based strategy
                    if strategy_type == 'regex_based':
                        if 'pattern' not in strategy_config:
                            raise ValidationError({'serial_decipher_logic': 'regex_based strategy must include pattern'})

                        # Validate field_mapping for regex_based strategy
                        if 'field_mapping' not in strategy_config:
                            raise ValidationError({'serial_decipher_logic': 'regex_based strategy must include field_mapping'})

                        field_mapping = strategy_config['field_mapping']
                        if not isinstance(field_mapping, dict):
                            raise ValidationError({'serial_decipher_logic': 'field_mapping must be a dictionary'})

                        # Required fields that must be present in field_mapping
                        required_fields = ['part_code', 'work_order', 'board']

                        # Check that all required fields are present
                        for field in required_fields:
                            if field not in field_mapping:
                                raise ValidationError({
                                    'serial_decipher_logic': f'field_mapping must contain required field: {field}'
                                })

                        # Validate part_code field (must always be required)
                        part_code_config = field_mapping['part_code']
                        if not isinstance(part_code_config, dict):
                            raise ValidationError({
                                'serial_decipher_logic': 'part_code field configuration must be a dictionary'
                            })

                        if part_code_config.get('required') is not True:
                            raise ValidationError({
                                'serial_decipher_logic': 'part_code field must have required: true'
                            })

                        # Validate work_order and board fields
                        for field_name in ['work_order', 'board']:
                            field_config = field_mapping[field_name]
                            if not isinstance(field_config, dict):
                                raise ValidationError({
                                    'serial_decipher_logic': f'{field_name} field configuration must be a dictionary'
                                })

                            # If required is false, default_value must be specified
                            if not field_config.get('required'):
                                if 'default_value' not in field_config:
                                    raise ValidationError({
                                        'serial_decipher_logic': f'{field_name} field with required: false must have default_value specified'
                                    })
                    
                    # Validate custom_logic strategy
                    elif strategy_type == 'custom_logic':
                        if 'parser_name' not in strategy_config:
                            raise ValidationError({'serial_decipher_logic': 'custom_logic strategy must include parser_name'})
                    
                    # Unknown strategy type
                    else:
                        raise ValidationError({'serial_decipher_logic': f'Unknown strategy type: {strategy_type}'})
                
                # If no strategy_config, validate ranges
                else:
                    # Check required fields
                    if 'serial_length' not in logic:
                        raise ValidationError({'serial_decipher_logic': 'Missing required field: serial_length'})
                    if 'ranges' not in logic:
                        raise ValidationError({'serial_decipher_logic': 'Missing required field: ranges'})

                    # Check ranges structure
                    ranges = logic['ranges']
                    required_range_fields = ['part_no', 'work_order_no']
                    for field in required_range_fields:
                        if field not in ranges:
                            raise ValidationError({'serial_decipher_logic': f'Missing required range field: {field}'})

                    # validate ranges
                    range_intervals = []
                    for field, config in ranges.items():
                        if 'start' not in config:
                            raise ValidationError({'serial_decipher_logic': f'Missing start index for range: {field}'})
                        if 'end' not in config:
                            raise ValidationError({'serial_decipher_logic': f'Missing end index for range: {field}'})

                        start = int(config['start'])
                        end = int(config['end'])

                        # Validate start is less than end
                        if start >= end:
                            raise ValidationError({
                                'serial_decipher_logic': f'Invalid range for {field}: start index must be less than end index'
                            })

                        # Check for overlapping ranges
                        for existing_field, (existing_start, existing_end) in range_intervals:
                            if not (end <= existing_start or start >= existing_end):
                                raise ValidationError({
                                    'serial_decipher_logic': f'Range overlap detected between {field} and {existing_field}'
                                })

                        range_intervals.append((field, (start, end)))

            except (TypeError, ValueError) as e:
                raise ValidationError({'serial_decipher_logic': f'Invalid JSON structure: {str(e)}'})

class Product(BaseEntity):
    """Model for products"""
    type_id = models.ForeignKey(
        ProductType,
        db_column='type_id',
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        related_name='products',
        help_text="Product type"
    )
    commodity = models.ForeignKey(
        'Commodity',
        on_delete=models.PROTECT,
        related_name='products',
        null=True,
        blank=True
    )
    components = models.ManyToManyField(Component, through='ProductComponent')

    # FIFO Configuration
    fifo_enabled = models.BooleanField(null=True, blank=True, help_text="Override FIFO tracking for this product")

    class Meta:
        db_table = 'mes_products'
        ordering = ['-updated_at']

    def __str__(self):
        return self.name


class ProductPart(BaseModel):
    """
    Model to store different part numbers for a product
    """
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='parts')
    part_no = models.CharField(max_length=100, unique=True)
    name = models.CharField(max_length=255, default='part name')
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        db_table = 'mes_product_parts'
        ordering = ['-updated_at']

    def __str__(self):
        return f"{self.part_no} - {self.product.name}"


class ProductComponent(BaseModel):
    """
    Model to store product-component relationships
    """
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='product_components')
    component = models.ForeignKey(Component, on_delete=models.CASCADE, related_name='products')
    quantity = models.IntegerField(default=1)
    position = models.CharField(max_length=100, blank=True)
    notes = models.TextField(blank=True)

    class Meta:
        db_table = 'mes_product_components'
        ordering = ['-updated_at']
        # unique_together = ('product', 'component', 'position')

    def __str__(self):
        return f"{self.product.code} - {self.component.name}"


class Scanner(BaseModel):
    """
    Model to store scanner information
    """
    code = models.CharField(max_length=100, unique=True)
    name = models.CharField(max_length=255)
    description = models.TextField(null=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        db_table = 'mes_scanners'
        ordering = ['-updated_at']

    def __str__(self):
        return self.name
